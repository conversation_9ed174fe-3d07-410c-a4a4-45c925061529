@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500;600&display=swap');
@import "tailwindcss";

/* Syntax highlighting for code blocks */
@import 'highlight.js/styles/github-dark.css';

@theme {
  --font-sans: Inter, system-ui, sans-serif;
  --font-mono: "JetBrains Mono", Consolas, monospace;

  --color-ds-primary-50: #eff6ff;
  --color-ds-primary-100: #dbeafe;
  --color-ds-primary-200: #bfdbfe;
  --color-ds-primary-300: #93c5fd;
  --color-ds-primary-400: #60a5fa;
  --color-ds-primary-500: #3b82f6;
  --color-ds-primary-600: #2563eb;
  --color-ds-primary-700: #1d4ed8;
  --color-ds-primary-800: #1e40af;
  --color-ds-primary-900: #1e3a8a;

  --color-de-primary-50: #f0fdf4;
  --color-de-primary-100: #dcfce7;
  --color-de-primary-200: #bbf7d0;
  --color-de-primary-300: #86efac;
  --color-de-primary-400: #4ade80;
  --color-de-primary-500: #22c55e;
  --color-de-primary-600: #16a34a;
  --color-de-primary-700: #15803d;
  --color-de-primary-800: #166534;
  --color-de-primary-900: #14532d;

  --color-dark-50: #f8fafc;
  --color-dark-100: #f1f5f9;
  --color-dark-200: #e2e8f0;
  --color-dark-300: #cbd5e1;
  --color-dark-400: #94a3b8;
  --color-dark-500: #64748b;
  --color-dark-600: #475569;
  --color-dark-700: #334155;
  --color-dark-800: #1e293b;
  --color-dark-900: #0f172a;
}

@layer base {
  html {
    scroll-behavior: smooth;
  }
  
  body {
    @apply font-sans antialiased;
    @apply bg-white dark:bg-dark-900;
    @apply text-gray-900 dark:text-gray-100;
    @apply transition-colors duration-300;
    font-size: 16px; /* Prevent zoom on iOS */
    line-height: 1.6;
  }

  /* Mobile-first responsive improvements */
  @media (max-width: 768px) {
    body {
      font-size: 16px;
      line-height: 1.5;
    }

    h1 { font-size: 1.875rem !important; }
    h2 { font-size: 1.5rem !important; }
    h3 { font-size: 1.25rem !important; }

    /* Better touch targets */
    button, a, input, select, textarea {
      min-height: 44px;
    }

    /* Improved spacing */
    .container {
      padding-left: 1rem;
      padding-right: 1rem;
    }

    /* Better card spacing on mobile */
    .card {
      margin-bottom: 1rem;
    }
  }

  /* Tablet improvements */
  @media (min-width: 769px) and (max-width: 1024px) {
    .container {
      padding-left: 1.5rem;
      padding-right: 1.5rem;
    }
  }
  
  code {
    @apply font-mono;
  }
  
  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
  }
  
  ::-webkit-scrollbar-track {
    @apply bg-gray-100 dark:bg-dark-800;
  }
  
  ::-webkit-scrollbar-thumb {
    @apply bg-gray-300 dark:bg-dark-600 rounded-full;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-400 dark:bg-dark-500;
  }
}

@layer components {
  /* Button styles */
  .btn-primary {
    @apply px-6 py-3 bg-ds-primary-600 hover:bg-ds-primary-700 text-white font-medium rounded-lg transition-colors duration-200;
  }
  
  .btn-secondary {
    @apply px-6 py-3 bg-gray-200 hover:bg-gray-300 dark:bg-dark-700 dark:hover:bg-dark-600 text-gray-900 dark:text-gray-100 font-medium rounded-lg transition-colors duration-200;
  }
  
  .btn-outline {
    @apply px-6 py-3 border-2 border-ds-primary-600 text-ds-primary-600 hover:bg-ds-primary-600 hover:text-white font-medium rounded-lg transition-all duration-200;
  }
  
  /* Card styles */
  .card {
    @apply bg-white dark:bg-dark-800 rounded-xl shadow-lg border border-gray-200 dark:border-dark-700 transition-all duration-300;
  }
  
  .card-hover {
    @apply hover:shadow-xl hover:scale-105 cursor-pointer;
  }
  
  /* Input styles */
  .input-field {
    @apply w-full px-4 py-3 border border-gray-300 dark:border-dark-600 rounded-lg bg-white dark:bg-dark-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-ds-primary-500 focus:border-transparent transition-all duration-200;
  }
  
  /* Progress bar */
  .progress-bar {
    @apply w-full bg-gray-200 dark:bg-dark-700 rounded-full h-2;
  }
  
  .progress-fill {
    @apply bg-gradient-to-r from-ds-primary-500 to-ds-primary-600 h-2 rounded-full transition-all duration-500;
  }
  
  /* Navigation styles */
  .nav-link {
    @apply px-4 py-2 text-gray-700 dark:text-gray-300 hover:text-ds-primary-600 dark:hover:text-ds-primary-400 font-medium transition-colors duration-200;
  }
  
  .nav-link-active {
    @apply text-ds-primary-600 dark:text-ds-primary-400 bg-ds-primary-50 dark:bg-ds-primary-900/20 rounded-lg;
  }
}
