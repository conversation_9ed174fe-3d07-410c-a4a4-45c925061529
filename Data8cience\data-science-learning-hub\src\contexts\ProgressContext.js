import React, { createContext, useContext, useState, useEffect } from 'react';

const ProgressContext = createContext();

export const useProgress = () => {
  const context = useContext(ProgressContext);
  if (!context) {
    throw new Error('useProgress must be used within a ProgressProvider');
  }
  return context;
};

const initialProgress = {
  currentModule: null,
  completedTopics: [],
  quizScores: {},
  certificates: [],
  bookmarks: [],
  timeSpent: {},
  lastAccessed: null,
  preferences: {
    track: null, // 'data-science' or 'data-engineering'
    difficulty: 'beginner',
    notifications: true
  }
};

export const ProgressProvider = ({ children }) => {
  const [progress, setProgress] = useState(() => {
    const saved = localStorage.getItem('learningProgress');
    return saved ? { ...initialProgress, ...JSON.parse(saved) } : initialProgress;
  });

  // Save to localStorage whenever progress changes
  useEffect(() => {
    localStorage.setItem('learningProgress', JSON.stringify(progress));
  }, [progress]);

  const updateProgress = (updates) => {
    setProgress(prev => ({
      ...prev,
      ...updates,
      lastAccessed: new Date().toISOString()
    }));
  };

  const completeModule = (moduleId) => {
    setProgress(prev => ({
      ...prev,
      completedTopics: [...new Set([...prev.completedTopics, moduleId])],
      lastAccessed: new Date().toISOString()
    }));
  };

  const saveQuizScore = (moduleId, score, totalQuestions) => {
    setProgress(prev => ({
      ...prev,
      quizScores: {
        ...prev.quizScores,
        [moduleId]: {
          score,
          totalQuestions,
          percentage: Math.round((score / totalQuestions) * 100),
          completedAt: new Date().toISOString()
        }
      },
      lastAccessed: new Date().toISOString()
    }));
  };

  const addCertificate = (certificateId, track, level) => {
    const certificate = {
      id: certificateId,
      track,
      level,
      earnedAt: new Date().toISOString(),
      title: `${track} - ${level} Level Completion`
    };

    setProgress(prev => ({
      ...prev,
      certificates: [...prev.certificates, certificate],
      lastAccessed: new Date().toISOString()
    }));
  };

  const toggleBookmark = (moduleId) => {
    setProgress(prev => ({
      ...prev,
      bookmarks: prev.bookmarks.includes(moduleId)
        ? prev.bookmarks.filter(id => id !== moduleId)
        : [...prev.bookmarks, moduleId],
      lastAccessed: new Date().toISOString()
    }));
  };

  const updateTimeSpent = (moduleId, timeInSeconds) => {
    setProgress(prev => ({
      ...prev,
      timeSpent: {
        ...prev.timeSpent,
        [moduleId]: (prev.timeSpent[moduleId] || 0) + timeInSeconds
      },
      lastAccessed: new Date().toISOString()
    }));
  };

  const updatePreferences = (newPreferences) => {
    setProgress(prev => ({
      ...prev,
      preferences: {
        ...prev.preferences,
        ...newPreferences
      },
      lastAccessed: new Date().toISOString()
    }));
  };

  const resetProgress = () => {
    setProgress(initialProgress);
    localStorage.removeItem('learningProgress');
  };

  const getModuleProgress = (moduleId) => {
    return {
      isCompleted: progress.completedTopics.includes(moduleId),
      isBookmarked: progress.bookmarks.includes(moduleId),
      quizScore: progress.quizScores[moduleId],
      timeSpent: progress.timeSpent[moduleId] || 0
    };
  };

  const getOverallProgress = (track) => {
    // This would be calculated based on available modules
    // For now, return basic stats
    const trackModules = progress.completedTopics.filter(id => id.startsWith(track));
    return {
      completedModules: trackModules.length,
      totalTimeSpent: Object.values(progress.timeSpent).reduce((sum, time) => sum + time, 0),
      averageQuizScore: Object.values(progress.quizScores).length > 0
        ? Object.values(progress.quizScores).reduce((sum, quiz) => sum + quiz.percentage, 0) / Object.values(progress.quizScores).length
        : 0,
      certificates: progress.certificates.filter(cert => cert.track === track).length
    };
  };

  const value = {
    progress,
    updateProgress,
    completeModule,
    saveQuizScore,
    addCertificate,
    toggleBookmark,
    updateTimeSpent,
    updatePreferences,
    resetProgress,
    getModuleProgress,
    getOverallProgress
  };

  return (
    <ProgressContext.Provider value={value}>
      {children}
    </ProgressContext.Provider>
  );
};
